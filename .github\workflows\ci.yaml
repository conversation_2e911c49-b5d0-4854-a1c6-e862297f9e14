name: CI

on:
  push:
    branches:
    - master
    - release*
  pull_request:
    branches:
    - master
    - release*

env:
  # Common versions
  GO_VERSION: '1.22'
  GOLANGCI_VERSION: 'v1.55.2'

jobs:
  golangci-lint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          submodules: true
      - name: Setup Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Verify go.mod is tidy
        run: |
          go mod tidy
          if [ -n "$(git status --porcelain go.mod go.sum)" ]; then
            echo "go.mod or go.sum is not tidy"
            git diff go.mod go.sum
            exit 1
          fi
      - name: Cache Go Dependencies
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-
      - name: Lint golang code
        uses: golangci/golangci-lint-action@2226d7cb06a077cd73e56eedd38eecad18e5d837 # v6.5.0
        with:
          version: ${{ env.GOLANGCI_VERSION }}
          args: --verbose

  build-and-test:
    name: ci-build
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          submodules: true

      - name: Setup Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules and build cache
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: |
            ~/go/pkg/mod
            ~/.cache/go-build
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-

      - name: Verify go.mod is tidy
        run: |
          go mod tidy
          if [ -n "$(git status --porcelain go.mod go.sum)" ]; then
            echo "go.mod or go.sum is not tidy"
            git diff go.mod go.sum
            exit 1
          fi
          
      - name: Build
        run: |
          make build

      - name: Test
        run: |
          make test
