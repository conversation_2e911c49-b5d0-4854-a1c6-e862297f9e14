name: Release

on:
  release:
    types:
      - created

env:
  GO_VERSION: '1.22'
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

jobs:
  build_and_upload:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        include:
          - os: linux
            arch: amd64
          - os: linux
            arch: arm64
          - os: darwin
            arch: amd64
          - os: darwin
            arch: arm64
          - os: windows
            arch: amd64
    env:
      GO_BUILD_ENV: GO111MODULE=on CGO_ENABLED=0
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0  # Needed for version.sh to work properly
          
      - name: Setup Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5.2.0
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true  # Enable built-in Go caching
          
      - name: Get ldflags
        id: get_ldflags
        run: |
          LDFLAGS=$(./version.sh)
          echo "LDFLAGS=${LDFLAGS}" >> $GITHUB_OUTPUT
          
      - name: Build kubectl-kruise
        run: |
          ${{ env.GO_BUILD_ENV }} GOOS=${{ matrix.os }} GOARCH=${{ matrix.arch }} \
            go build -ldflags "${{ steps.get_ldflags.outputs.LDFLAGS }}" \
            -o _bin/kubectl-kruise/${{ matrix.os }}-${{ matrix.arch }}/kubectl-kruise${{ matrix.os == 'windows' && '.exe' || '' }} \
            ./cmd/plugin/main.go
            
      - name: Build resourcedistribution-generator
        run: |
          ${{ env.GO_BUILD_ENV }} GOOS=${{ matrix.os }} GOARCH=${{ matrix.arch }} \
            go build -ldflags "${{ steps.get_ldflags.outputs.LDFLAGS }}" \
            -o _bin/resourcedistribution-generator/${{ matrix.os }}-${{ matrix.arch }}/resourcedistributiongenerator${{ matrix.os == 'windows' && '.exe' || '' }} \
            ./cmd/resourcedistributiongenerator/main.go
            
      - name: Package artifacts
        run: |
          # Package kubectl-kruise
          cd _bin/kubectl-kruise/${{ matrix.os }}-${{ matrix.arch }}
          cp ../../../LICENSE .
          cp ../../../README.md .
          if [ "${{ matrix.os }}" = "windows" ]; then
            zip -r kubectl-kruise-${{ matrix.os }}-${{ matrix.arch }}.zip .
          else
            tar -czf kubectl-kruise-${{ matrix.os }}-${{ matrix.arch }}.tar.gz .
          fi
          cd ../../..
          
          # Package resourcedistribution-generator
          cd _bin/resourcedistribution-generator/${{ matrix.os }}-${{ matrix.arch }}
          if [ "${{ matrix.os }}" = "windows" ]; then
            zip -r resourcedistribution-generator-${{ matrix.os }}-${{ matrix.arch }}.zip .
          else
            tar -czf resourcedistribution-generator-${{ matrix.os }}-${{ matrix.arch }}.tar.gz .
          fi
          cd ../../..
          
      - name: Generate checksums
        run: |
          cd _bin
          find . -name "*.tar.gz" -o -name "*.zip" | xargs sha256sum > sha256-${{ matrix.os }}-${{ matrix.arch }}.txt
          
      - name: Upload checksums artifact
        uses: actions/upload-artifact@v4.4.3
        with:
          name: sha256sums-${{ matrix.os }}-${{ matrix.arch }}
          path: _bin/sha256-${{ matrix.os }}-${{ matrix.arch }}.txt
          retention-days: 1
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4.4.3
        with:
          name: binaries-${{ matrix.os }}-${{ matrix.arch }}
          path: |
            _bin/kubectl-kruise/${{ matrix.os }}-${{ matrix.arch }}/*.tar.gz
            _bin/kubectl-kruise/${{ matrix.os }}-${{ matrix.arch }}/*.zip
            _bin/resourcedistribution-generator/${{ matrix.os }}-${{ matrix.arch }}/*.tar.gz
            _bin/resourcedistribution-generator/${{ matrix.os }}-${{ matrix.arch }}/*.zip
          retention-days: 1

  upload-release-assets:
    needs: build_and_upload
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        
      - name: Download all artifacts
        uses: actions/download-artifact@v4.1.7
        with:
          pattern: "*"
          merge-multiple: true
          path: artifacts
          
      - name: Prepare release assets
        run: |
          mkdir -p release-assets
          
          # Move binary archives to release assets
          find artifacts -name "*.tar.gz" -o -name "*.zip" | while read file; do
            filename=$(basename "$file")
            # Add version tag to filename
            name_part="${filename%.*}"
            ext="${filename##*.}"
            if [[ "$filename" == *.tar.gz ]]; then
              ext="tar.gz"
              name_part="${filename%.tar.gz}"
            fi
            cp "$file" "release-assets/${name_part}-${GITHUB_REF_NAME}.${ext}"
          done
          
          # Combine all checksums
          cat artifacts/sha256-*.txt > release-assets/sha256sums-${GITHUB_REF_NAME}.txt
          
      - name: Upload release assets
        uses: softprops/action-gh-release@v2.0.8
        with:
          files: release-assets/*
          fail_on_unmatched_files: true
          
      - name: Update kubectl plugin version in krew-index
        uses: rajatjindal/krew-release-bot@v0.0.46
