run:
  concurrency: 4
  deadline: 2m
  skip-dirs:
    - vendor

linters:
  disable-all: true
  enable:
    - gofmt
    - govet
    - goimports
    - ineffassign
    - misspell
    - vet
    - unconvert

linters-settings:
  golint:
    # minimal confidence for issues, default is 0.8
    min-confidence: 0.8
  gofmt:
    # simplify code: gofmt with `-s` option, true by default
    simplify: true
  goimports:
    #local-prefixes: github.com/openkruise/kruise-tools

issues:
  exclude-use-default: false
  exclude-rules:
    # We don't check metrics naming in the tests.
    - path: _test\.go
      linters:
        - promlinter
