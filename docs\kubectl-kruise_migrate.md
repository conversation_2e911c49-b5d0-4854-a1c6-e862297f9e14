## kubectl-kruise migrate

Migrate from K8s original workloads to Kruise workloads

### Synopsis

Migrate from K8s original workloads to Kruise workloads

```
kubectl-kruise migrate [DST_KIND] --from [SRC_KIND] [flags]
```

### Examples

```

	# Create an empty CloneSet from an existing Deployment.
	kubectl-kruise migrate CloneSet --from Deployment -n default --dst-name deployment-name --create

	# Create a same replicas CloneSet from an existing Deployment.
	kubectl-kruise migrate CloneSet --from Deployment -n default --dst-name deployment-name --create --copy

	# Migrate replicas from an existing Deployment to an existing CloneSet.
	kubectl-kruise migrate CloneSet --from Deployment -n default --src-name cloneset-name --dst-name deployment-name --replicas 10 --max-surge=2

```

### Options

```
      --copy                    Copy replicas from src workload when create.
      --create                  Create dst workload with replicas=0 from src workload.
      --dst-name string         Name of the destination workload.
      --from string             Type of the source workload (e.g. Deployment).
  -h, --help                    help for migrate
      --max-surge int32         Max surge during migration. (default 1)
      --replicas int32          The replicas needs to migrate, -1 indicates all replicas in src workload. (default -1)
      --src-name string         Name of the source workload.
      --timeout-seconds int32   Timeout seconds for migration, -1 indicates no limited. (default -1)
```

### Options inherited from parent commands

```
      --as string                      Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
      --as-group stringArray           Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
      --as-uid string                  UID to impersonate for the operation.
      --cache-dir string               Default cache directory (default "$HOME/.kube/cache")
      --certificate-authority string   Path to a cert file for the certificate authority
      --client-certificate string      Path to a client certificate file for TLS
      --client-key string              Path to a client key file for TLS
      --cluster string                 The name of the kubeconfig cluster to use
      --context string                 The name of the kubeconfig context to use
      --disable-compression            If true, opt-out of response compression for all requests to the server
      --insecure-skip-tls-verify       If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
      --kubeconfig string              Path to the kubeconfig file to use for CLI requests.
      --match-server-version           Require server version to match client version
  -n, --namespace string               If present, the namespace scope for this CLI request
      --password string                Password for basic authentication to the API server
      --profile string                 Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex) (default "none")
      --profile-output string          Name of the file to write the profile to (default "profile.pprof")
      --request-timeout string         The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests. (default "0")
  -s, --server string                  The address and port of the Kubernetes API server
      --tls-server-name string         Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
      --token string                   Bearer token for authentication to the API server
      --user string                    The name of the kubeconfig user to use
      --username string                Username for basic authentication to the API server
      --warnings-as-errors             Treat warnings received from the server as errors and exit with a non-zero exit code
```

### SEE ALSO

* [kubectl-kruise](kubectl-kruise.md)	 - kubectl-kruise controls the OpenKruise CRs

###### Auto generated by spf13/cobra on 12-Mar-2025
