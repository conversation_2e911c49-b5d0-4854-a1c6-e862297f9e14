## kubectl-kruise rollout

Manage the rollout of a resource

### Synopsis

Manage the rollout of a resource.
        
 Valid resource types include:

        * deployments
        * daemonsets
        * statefulsets
        * clonesets
        * statefulsets.apps.kruise.io
        * daemonsets.apps.kruise.io
        * rollouts.rollouts.kruise.io

```
kubectl-kruise rollout SUBCOMMAND
```

### Examples

```
  # Rollback to the previous cloneset
  kubectl-kruise rollout undo cloneset/abc
  
  # Check the rollout status of a daemonset
  kubectl-kruise rollout status daemonset/foo
```

### Options

```
  -h, --help   help for rollout
```

### Options inherited from parent commands

```
      --as string                      Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
      --as-group stringArray           Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
      --as-uid string                  UID to impersonate for the operation.
      --cache-dir string               Default cache directory (default "$HOME/.kube/cache")
      --certificate-authority string   Path to a cert file for the certificate authority
      --client-certificate string      Path to a client certificate file for TLS
      --client-key string              Path to a client key file for TLS
      --cluster string                 The name of the kubeconfig cluster to use
      --context string                 The name of the kubeconfig context to use
      --disable-compression            If true, opt-out of response compression for all requests to the server
      --insecure-skip-tls-verify       If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
      --kubeconfig string              Path to the kubeconfig file to use for CLI requests.
      --match-server-version           Require server version to match client version
  -n, --namespace string               If present, the namespace scope for this CLI request
      --password string                Password for basic authentication to the API server
      --profile string                 Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex) (default "none")
      --profile-output string          Name of the file to write the profile to (default "profile.pprof")
      --request-timeout string         The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests. (default "0")
  -s, --server string                  The address and port of the Kubernetes API server
      --tls-server-name string         Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
      --token string                   Bearer token for authentication to the API server
      --user string                    The name of the kubeconfig user to use
      --username string                Username for basic authentication to the API server
      --warnings-as-errors             Treat warnings received from the server as errors and exit with a non-zero exit code
```

### SEE ALSO

* [kubectl-kruise](kubectl-kruise.md)	 - kubectl-kruise controls the OpenKruise CRs
* [kubectl-kruise rollout approve](kubectl-kruise_rollout_approve.md)	 - Approve a resource
* [kubectl-kruise rollout history](kubectl-kruise_rollout_history.md)	 - View rollout history
* [kubectl-kruise rollout pause](kubectl-kruise_rollout_pause.md)	 - Mark the provided resource as paused
* [kubectl-kruise rollout restart](kubectl-kruise_rollout_restart.md)	 - Restart a resource
* [kubectl-kruise rollout resume](kubectl-kruise_rollout_resume.md)	 - Resume a paused resource
* [kubectl-kruise rollout status](kubectl-kruise_rollout_status.md)	 - Show the status of the rollout
* [kubectl-kruise rollout undo](kubectl-kruise_rollout_undo.md)	 - Undo a previous rollout

###### Auto generated by spf13/cobra on 12-Mar-2025
