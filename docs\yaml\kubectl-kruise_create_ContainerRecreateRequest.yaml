name: kube<PERSON><PERSON>-kruise create ContainerRecreateRequest
synopsis: Create a crr with the specified name.
description: Create a crr with the specified name.
usage: kubect<PERSON>-kruise create ContainerRecreateRequest NAME --pod=podName [--containers=container]
options:
    - name: allow-missing-template-keys
      default_value: "true"
      usage: |
        If true, ignore any errors in templates when a field or map key is missing in the template. Only applies to golang and jsonpath output formats.
    - name: containers
      default_value: '[]'
      usage: The containers those need to restarted.
    - name: dry-run
      default_value: none
      usage: |
        Must be "none", "server", or "client". If client strategy, only print the object that would be sent, without sending it. If server strategy, submit server-side request without persisting the resource.
    - name: field-manager
      default_value: kubectl kruise-create
      usage: Name of the manager used to track field ownership.
    - name: help
      shorthand: h
      default_value: "false"
      usage: help for ContainerRecreateRequest
    - name: minStartedSeconds
      shorthand: m
      default_value: "0"
      usage: |
        Minimum number of seconds for which a newly created container should be started and ready without any of its container crashing, for it to be considered Succeeded.Defaults to 0 (container will be considered Succeeded as soon as it is started and ready)
    - name: output
      shorthand: o
      usage: |
        Output format. One of: (json, yaml, name, go-template, go-template-file, template, templatefile, jsonpath, jsonpath-as-json, jsonpath-file).
    - name: pod
      shorthand: p
      usage: The name of the pod).
    - name: save-config
      default_value: "false"
      usage: |
        If true, the configuration of current object will be saved in its annotation. Otherwise, the annotation will be unchanged. This flag is useful when you want to perform kubectl apply on this object in the future.
    - name: show-managed-fields
      default_value: "false"
      usage: |
        If true, keep the managedFields when printing objects in JSON or YAML format.
    - name: template
      usage: |
        Template string or path to template file to use when -o=go-template, -o=go-template-file. The template format is golang templates [http://golang.org/pkg/text/template/#pkg-overview].
    - name: unreadyGracePeriodSeconds
      shorthand: u
      default_value: "0"
      usage: |
        UnreadyGracePeriodSeconds is the optional duration in seconds to mark Pod as not ready over this duration before executing preStop hook and stopping the container
    - name: validate
      default_value: strict
      usage: |-
        Must be one of: strict (or true), warn, ignore (or false).
        		"true" or "strict" will use a schema to validate the input and fail the request if invalid. It will perform server side validation if ServerSideFieldValidation is enabled on the api-server, but will fall back to less reliable client-side validation if not.
        		"warn" will warn about unknown or duplicate fields without blocking the request if server-side field validation is enabled on the API server, and behave as "ignore" otherwise.
        		"false" or "ignore" will not perform any schema validation, silently dropping any unknown or duplicate fields.
inherited_options:
    - name: as
      usage: |
        Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
    - name: as-group
      default_value: '[]'
      usage: |
        Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
    - name: as-uid
      usage: UID to impersonate for the operation.
    - name: cache-dir
      default_value: $HOME/.kube/cache
      usage: Default cache directory
    - name: certificate-authority
      usage: Path to a cert file for the certificate authority
    - name: client-certificate
      usage: Path to a client certificate file for TLS
    - name: client-key
      usage: Path to a client key file for TLS
    - name: cluster
      usage: The name of the kubeconfig cluster to use
    - name: context
      usage: The name of the kubeconfig context to use
    - name: disable-compression
      default_value: "false"
      usage: |
        If true, opt-out of response compression for all requests to the server
    - name: insecure-skip-tls-verify
      default_value: "false"
      usage: |
        If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
    - name: kubeconfig
      usage: Path to the kubeconfig file to use for CLI requests.
    - name: match-server-version
      default_value: "false"
      usage: Require server version to match client version
    - name: namespace
      shorthand: "n"
      usage: If present, the namespace scope for this CLI request
    - name: password
      usage: Password for basic authentication to the API server
    - name: profile
      default_value: none
      usage: |
        Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex)
    - name: profile-output
      default_value: profile.pprof
      usage: Name of the file to write the profile to
    - name: request-timeout
      default_value: "0"
      usage: |
        The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests.
    - name: server
      shorthand: s
      usage: The address and port of the Kubernetes API server
    - name: tls-server-name
      usage: |
        Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
    - name: token
      usage: Bearer token for authentication to the API server
    - name: user
      usage: The name of the kubeconfig user to use
    - name: username
      usage: Username for basic authentication to the API server
    - name: warnings-as-errors
      default_value: "false"
      usage: |
        Treat warnings received from the server as errors and exit with a non-zero exit code
example: "  NOTE: The default value of CRR is:\n  strategy:\n  failurePolicy: Fail\n  orderedRecreate: false\n  unreadyGracePeriodSeconds: 3\n  activeDeadlineSeconds: 300\n  ttlSecondsAfterFinished: 1800\n  # Create a crr with default value to restart all containers in pod-1\n  kubectl kruise create ContainerRecreateRequest my-crr --namespace=ns --pod=pod-1\n  \n  # Create a crr with default value to restart  container-1 in pod-1\n  kubectl kruise create ContainerRecreateRequest my-crr --namespace=ns --pod=pod-1 --containers=container-1\n  \n  # Create a crr with unreadyGracePeriodSeconds 5 and terminationGracePeriodSeconds 30 to restart  container-1 in pod-1\n  kubectl kruise create ContainerRecreateRequest my-crr --namespace=ns --pod=pod-1 --containers=container-1 --unreadyGracePeriodSeconds=5 --terminationGracePeriodSeconds=30"
see_also:
    - kubectl-kruise create - Create a resource from a file or from stdin.
