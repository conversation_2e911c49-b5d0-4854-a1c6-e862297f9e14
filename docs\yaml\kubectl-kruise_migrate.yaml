name: kubectl-kruise migrate
synopsis: Migrate from K8s original workloads to Kruise workloads
description: Migrate from K8s original workloads to Kruise workloads
usage: kubectl-kruise migrate [DST_KIND] --from [SRC_KIND] [flags]
options:
    - name: copy
      default_value: "false"
      usage: Copy replicas from src workload when create.
    - name: create
      default_value: "false"
      usage: Create dst workload with replicas=0 from src workload.
    - name: dst-name
      usage: Name of the destination workload.
    - name: from
      usage: Type of the source workload (e.g. Deployment).
    - name: help
      shorthand: h
      default_value: "false"
      usage: help for migrate
    - name: max-surge
      default_value: "1"
      usage: Max surge during migration.
    - name: replicas
      default_value: "-1"
      usage: |
        The replicas needs to migrate, -1 indicates all replicas in src workload.
    - name: src-name
      usage: Name of the source workload.
    - name: timeout-seconds
      default_value: "-1"
      usage: Timeout seconds for migration, -1 indicates no limited.
inherited_options:
    - name: as
      usage: |
        Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
    - name: as-group
      default_value: '[]'
      usage: |
        Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
    - name: as-uid
      usage: UID to impersonate for the operation.
    - name: cache-dir
      default_value: $HOME/.kube/cache
      usage: Default cache directory
    - name: certificate-authority
      usage: Path to a cert file for the certificate authority
    - name: client-certificate
      usage: Path to a client certificate file for TLS
    - name: client-key
      usage: Path to a client key file for TLS
    - name: cluster
      usage: The name of the kubeconfig cluster to use
    - name: context
      usage: The name of the kubeconfig context to use
    - name: disable-compression
      default_value: "false"
      usage: |
        If true, opt-out of response compression for all requests to the server
    - name: insecure-skip-tls-verify
      default_value: "false"
      usage: |
        If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
    - name: kubeconfig
      usage: Path to the kubeconfig file to use for CLI requests.
    - name: match-server-version
      default_value: "false"
      usage: Require server version to match client version
    - name: namespace
      shorthand: "n"
      usage: If present, the namespace scope for this CLI request
    - name: password
      usage: Password for basic authentication to the API server
    - name: profile
      default_value: none
      usage: |
        Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex)
    - name: profile-output
      default_value: profile.pprof
      usage: Name of the file to write the profile to
    - name: request-timeout
      default_value: "0"
      usage: |
        The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests.
    - name: server
      shorthand: s
      usage: The address and port of the Kubernetes API server
    - name: tls-server-name
      usage: |
        Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
    - name: token
      usage: Bearer token for authentication to the API server
    - name: user
      usage: The name of the kubeconfig user to use
    - name: username
      usage: Username for basic authentication to the API server
    - name: warnings-as-errors
      default_value: "false"
      usage: |
        Treat warnings received from the server as errors and exit with a non-zero exit code
example: |4
    	# Create an empty CloneSet from an existing Deployment.
    	kubectl-kruise migrate CloneSet --from Deployment -n default --dst-name deployment-name --create

    	# Create a same replicas CloneSet from an existing Deployment.
    	kubectl-kruise migrate CloneSet --from Deployment -n default --dst-name deployment-name --create --copy

    	# Migrate replicas from an existing Deployment to an existing CloneSet.
    	kubectl-kruise migrate CloneSet --from Deployment -n default --src-name cloneset-name --dst-name deployment-name --replicas 10 --max-surge=2
see_also:
    - kubectl-kruise - kubectl-kruise controls the OpenKruise CRs
