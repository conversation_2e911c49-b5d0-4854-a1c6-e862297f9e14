name: kubectl-kruise rollout status
synopsis: Show the status of the rollout
description: |-
    Show the status of the rollout.

     By default 'rollout status' will watch the status of the latest rollout until it's done. If you don't want to wait for the rollout to finish then you can use --watch=false. Note that if a new rollout starts in-between, then 'rollout status' will continue watching the latest revision. If you want to pin to a specific revision and abort if it is rolled over by another revision, use --revision=N where N is the revision you need to watch for.
usage: kubectl-kruise rollout status (TYPE NAME | TYPE/NAME) [flags]
options:
    - name: detail
      shorthand: d
      default_value: "false"
      usage: Show the detail status of the rollout.
    - name: filename
      shorthand: f
      default_value: '[]'
      usage: |
        Filename, directory, or URL to files identifying the resource to get from a server.
    - name: help
      shorthand: h
      default_value: "false"
      usage: help for status
    - name: kustomize
      shorthand: k
      usage: |
        Process the kustomization directory. This flag can't be used together with -f or -R.
    - name: recursive
      shorthand: R
      default_value: "false"
      usage: |
        Process the directory used in -f, --filename recursively. Useful when you want to manage related manifests organized within the same directory.
    - name: revision
      default_value: "0"
      usage: |
        Pin to a specific revision for showing its status. Defaults to 0 (last revision).
    - name: timeout
      default_value: 0s
      usage: |
        The length of time to wait before ending watch, zero means never. Any other values should contain a corresponding time unit (e.g. 1s, 2m, 3h).
    - name: watch
      shorthand: w
      default_value: "true"
      usage: Watch the status of the rollout until it's done.
inherited_options:
    - name: as
      usage: |
        Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
    - name: as-group
      default_value: '[]'
      usage: |
        Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
    - name: as-uid
      usage: UID to impersonate for the operation.
    - name: cache-dir
      default_value: $HOME/.kube/cache
      usage: Default cache directory
    - name: certificate-authority
      usage: Path to a cert file for the certificate authority
    - name: client-certificate
      usage: Path to a client certificate file for TLS
    - name: client-key
      usage: Path to a client key file for TLS
    - name: cluster
      usage: The name of the kubeconfig cluster to use
    - name: context
      usage: The name of the kubeconfig context to use
    - name: disable-compression
      default_value: "false"
      usage: |
        If true, opt-out of response compression for all requests to the server
    - name: insecure-skip-tls-verify
      default_value: "false"
      usage: |
        If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
    - name: kubeconfig
      usage: Path to the kubeconfig file to use for CLI requests.
    - name: match-server-version
      default_value: "false"
      usage: Require server version to match client version
    - name: namespace
      shorthand: "n"
      usage: If present, the namespace scope for this CLI request
    - name: password
      usage: Password for basic authentication to the API server
    - name: profile
      default_value: none
      usage: |
        Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex)
    - name: profile-output
      default_value: profile.pprof
      usage: Name of the file to write the profile to
    - name: request-timeout
      default_value: "0"
      usage: |
        The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests.
    - name: server
      shorthand: s
      usage: The address and port of the Kubernetes API server
    - name: tls-server-name
      usage: |
        Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
    - name: token
      usage: Bearer token for authentication to the API server
    - name: user
      usage: The name of the kubeconfig user to use
    - name: username
      usage: Username for basic authentication to the API server
    - name: warnings-as-errors
      default_value: "false"
      usage: |
        Treat warnings received from the server as errors and exit with a non-zero exit code
example: "  # Watch the rollout status of a deployment\n  kubectl-kruise rollout status deployment/nginx\n  \n  # Watch the rollout status of a cloneset\n  kubectl-kruise rollout status cloneset/nginx\n  \n  # Watch the rollout status of a advanced statefulset\n  kubectl-kruise rollout status asts/nginx"
see_also:
    - kubectl-kruise rollout - Manage the rollout of a resource
