name: kubectl-kruise set env
synopsis: Update environment variables on a pod template
description: |-
    Update environment variables on a pod template.

     List environment variable definitions in one or more pods, pod templates. Add, update, or remove container environment variable definitions in one or more pod templates (within replication controllers or deployment configurations). View or modify the environment variable definitions on all containers in the specified pods or pod templates, or just those that match a wildcard.

     If "--env -" is passed, environment variables can be read from STDIN using the standard env syntax.

     Possible resources include (case insensitive):

            pod (po), deployment (deploy), statefulset (sts), daemonset (ds), replicaset (rs), cloneset (clone), statefulset.apps.kruise.io (asts), daemonset.apps.kruise.io (ads)
usage: kubectl-kruise set env RESOURCE/NAME KEY_1=VAL_1 ... KEY_N=VAL_N
options:
    - name: all
      default_value: "false"
      usage: |
        If true, select all resources in the namespace of the specified resource types
    - name: allow-missing-template-keys
      default_value: "true"
      usage: |
        If true, ignore any errors in templates when a field or map key is missing in the template. Only applies to golang and jsonpath output formats.
    - name: containers
      shorthand: c
      default_value: '*'
      usage: |
        The names of containers in the selected pod templates to change - may use wildcards
    - name: dry-run
      default_value: none
      usage: |
        Must be "none", "server", or "client". If client strategy, only print the object that would be sent, without sending it. If server strategy, submit server-side request without persisting the resource.
    - name: env
      shorthand: e
      default_value: '[]'
      usage: |
        Specify a key-value pair for an environment variable to set into each container.
    - name: filename
      shorthand: f
      default_value: '[]'
      usage: |
        Filename, directory, or URL to files the resource to update the env
    - name: from
      usage: |
        The name of a resource from which to inject environment variables
    - name: help
      shorthand: h
      default_value: "false"
      usage: help for env
    - name: keys
      default_value: '[]'
      usage: |
        Comma-separated list of keys to import from specified resource
    - name: kustomize
      shorthand: k
      usage: |
        Process the kustomization directory. This flag can't be used together with -f or -R.
    - name: list
      default_value: "false"
      usage: |
        If true, display the environment and any changes in the standard format. this flag will removed when we have kubectl view env.
    - name: local
      default_value: "false"
      usage: |
        If true, set env will NOT contact api-server but run locally.
    - name: output
      shorthand: o
      usage: |
        Output format. One of: (json, yaml, name, go-template, go-template-file, template, templatefile, jsonpath, jsonpath-as-json, jsonpath-file).
    - name: overwrite
      default_value: "true"
      usage: |
        If true, allow environment to be overwritten, otherwise reject updates that overwrite existing environment.
    - name: prefix
      usage: Prefix to append to variable names
    - name: recursive
      shorthand: R
      default_value: "false"
      usage: |
        Process the directory used in -f, --filename recursively. Useful when you want to manage related manifests organized within the same directory.
    - name: resolve
      default_value: "false"
      usage: |
        If true, show secret or configmap references when listing variables
    - name: selector
      shorthand: l
      usage: Selector (label query) to filter on
    - name: show-managed-fields
      default_value: "false"
      usage: |
        If true, keep the managedFields when printing objects in JSON or YAML format.
    - name: template
      usage: |
        Template string or path to template file to use when -o=go-template, -o=go-template-file. The template format is golang templates [http://golang.org/pkg/text/template/#pkg-overview].
inherited_options:
    - name: as
      usage: |
        Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
    - name: as-group
      default_value: '[]'
      usage: |
        Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
    - name: as-uid
      usage: UID to impersonate for the operation.
    - name: cache-dir
      default_value: $HOME/.kube/cache
      usage: Default cache directory
    - name: certificate-authority
      usage: Path to a cert file for the certificate authority
    - name: client-certificate
      usage: Path to a client certificate file for TLS
    - name: client-key
      usage: Path to a client key file for TLS
    - name: cluster
      usage: The name of the kubeconfig cluster to use
    - name: context
      usage: The name of the kubeconfig context to use
    - name: disable-compression
      default_value: "false"
      usage: |
        If true, opt-out of response compression for all requests to the server
    - name: insecure-skip-tls-verify
      default_value: "false"
      usage: |
        If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
    - name: kubeconfig
      usage: Path to the kubeconfig file to use for CLI requests.
    - name: match-server-version
      default_value: "false"
      usage: Require server version to match client version
    - name: namespace
      shorthand: "n"
      usage: If present, the namespace scope for this CLI request
    - name: password
      usage: Password for basic authentication to the API server
    - name: profile
      default_value: none
      usage: |
        Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex)
    - name: profile-output
      default_value: profile.pprof
      usage: Name of the file to write the profile to
    - name: request-timeout
      default_value: "0"
      usage: |
        The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests.
    - name: server
      shorthand: s
      usage: The address and port of the Kubernetes API server
    - name: tls-server-name
      usage: |
        Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
    - name: token
      usage: Bearer token for authentication to the API server
    - name: user
      usage: The name of the kubeconfig user to use
    - name: username
      usage: Username for basic authentication to the API server
    - name: warnings-as-errors
      default_value: "false"
      usage: |
        Treat warnings received from the server as errors and exit with a non-zero exit code
example: "  # Update cloneset 'sample' with a new environment variable\n  kubectl-kruise set env cloneset/sample STORAGE_DIR=/local\n  \n  # List the environment variables defined on a cloneset 'sample'\n  kubectl-kruise set env cloneset/sample --list\n  \n  # List the environment variables defined on all pods\n  kubectl-kruise set env pods --all --list\n  \n  # Output modified cloneset in YAML, and does not alter the object on the server\n  kubectl-kruise set env cloneset/sample STORAGE_DIR=/data -o yaml\n  \n  # Update all containers in all replication controllers in the project to have ENV=prod\n  kubectl-kruise set env rc --all ENV=prod\n  \n  # Import environment from a secret\n  kubectl-kruise set env --from=secret/mysecret cloneset/sample\n  \n  # Import environment from a config map with a prefix\n  kubectl-kruise set env --from=configmap/myconfigmap --prefix=MYSQL_ cloneset/sample\n  \n  # Import specific keys from a config map\n  kubectl-kruise set env --keys=my-example-key --from=configmap/myconfigmap cloneset/sample\n  \n  # Remove the environment variable ENV from container 'c1' in all deployment configs\n  kubectl-kruise set env clonesets --all --containers=\"c1\" ENV-\n  \n  # Remove the environment variable ENV from a deployment definition on disk and\n  # update the deployment config on the server\n  kubectl-kruise set env -f deploy.json ENV-\n  \n  # Set some of the local shell environment into a deployment config on the server\n  env | grep RAILS_ | kubectl-kruise set env -e - cloneset/sample"
see_also:
    - kubectl-kruise set - Set specific features on objects
