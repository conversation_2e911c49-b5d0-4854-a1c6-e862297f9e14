name: kubectl-kruise set serviceaccount
synopsis: Update ServiceAccount of a resource
description: |-
    Update ServiceAccount of pod template resources.

     Possible resources (case insensitive) can be:

     replicationcontroller (rc), deployment (deploy), daemonset (ds), job, replicaset (rs), statefulset, cloneset (cs)
usage: kubectl-kruise set serviceaccount (-f FILENAME | TYPE NAME) SERVICE_ACCOUNT
options:
    - name: all
      default_value: "false"
      usage: |
        Select all resources, including uninitialized ones, in the namespace of the specified resource types
    - name: allow-missing-template-keys
      default_value: "true"
      usage: |
        If true, ignore any errors in templates when a field or map key is missing in the template. Only applies to golang and jsonpath output formats.
    - name: dry-run
      default_value: none
      usage: |
        Must be "none", "server", or "client". If client strategy, only print the object that would be sent, without sending it. If server strategy, submit server-side request without persisting the resource.
    - name: filename
      shorthand: f
      default_value: '[]'
      usage: |
        Filename, directory, or URL to files identifying the resource to get from a server.
    - name: help
      shorthand: h
      default_value: "false"
      usage: help for serviceaccount
    - name: kustomize
      shorthand: k
      usage: |
        Process the kustomization directory. This flag can't be used together with -f or -R.
    - name: local
      default_value: "false"
      usage: |
        If true, set serviceaccount will NOT contact api-server but run locally.
    - name: output
      shorthand: o
      usage: |
        Output format. One of: (json, yaml, name, go-template, go-template-file, template, templatefile, jsonpath, jsonpath-as-json, jsonpath-file).
    - name: record
      default_value: "false"
      usage: |
        Record current kubectl command in the resource annotation. If set to false, do not record the command. If set to true, record the command. If not set, default to updating the existing annotation value only if one already exists.
    - name: recursive
      shorthand: R
      default_value: "false"
      usage: |
        Process the directory used in -f, --filename recursively. Useful when you want to manage related manifests organized within the same directory.
    - name: show-managed-fields
      default_value: "false"
      usage: |
        If true, keep the managedFields when printing objects in JSON or YAML format.
    - name: template
      usage: |
        Template string or path to template file to use when -o=go-template, -o=go-template-file. The template format is golang templates [http://golang.org/pkg/text/template/#pkg-overview].
inherited_options:
    - name: as
      usage: |
        Username to impersonate for the operation. User could be a regular user or a service account in a namespace.
    - name: as-group
      default_value: '[]'
      usage: |
        Group to impersonate for the operation, this flag can be repeated to specify multiple groups.
    - name: as-uid
      usage: UID to impersonate for the operation.
    - name: cache-dir
      default_value: $HOME/.kube/cache
      usage: Default cache directory
    - name: certificate-authority
      usage: Path to a cert file for the certificate authority
    - name: client-certificate
      usage: Path to a client certificate file for TLS
    - name: client-key
      usage: Path to a client key file for TLS
    - name: cluster
      usage: The name of the kubeconfig cluster to use
    - name: context
      usage: The name of the kubeconfig context to use
    - name: disable-compression
      default_value: "false"
      usage: |
        If true, opt-out of response compression for all requests to the server
    - name: insecure-skip-tls-verify
      default_value: "false"
      usage: |
        If true, the server's certificate will not be checked for validity. This will make your HTTPS connections insecure
    - name: kubeconfig
      usage: Path to the kubeconfig file to use for CLI requests.
    - name: match-server-version
      default_value: "false"
      usage: Require server version to match client version
    - name: namespace
      shorthand: "n"
      usage: If present, the namespace scope for this CLI request
    - name: password
      usage: Password for basic authentication to the API server
    - name: profile
      default_value: none
      usage: |
        Name of profile to capture. One of (none|cpu|heap|goroutine|threadcreate|block|mutex)
    - name: profile-output
      default_value: profile.pprof
      usage: Name of the file to write the profile to
    - name: request-timeout
      default_value: "0"
      usage: |
        The length of time to wait before giving up on a single server request. Non-zero values should contain a corresponding time unit (e.g. 1s, 2m, 3h). A value of zero means don't timeout requests.
    - name: server
      shorthand: s
      usage: The address and port of the Kubernetes API server
    - name: tls-server-name
      usage: |
        Server name to use for server certificate validation. If it is not provided, the hostname used to contact the server is used
    - name: token
      usage: Bearer token for authentication to the API server
    - name: user
      usage: The name of the kubeconfig user to use
    - name: username
      usage: Username for basic authentication to the API server
    - name: warnings-as-errors
      default_value: "false"
      usage: |
        Treat warnings received from the server as errors and exit with a non-zero exit code
example: "  # Set Deployment nginx-deployment's ServiceAccount to serviceaccount1\n  kubectl-kruise set serviceaccount cloneset sample serviceaccount1\n  \n  # Print the result (in yaml format) of updated cloneset with serviceaccount from local file, without hitting apiserver\n  kubectl-kruise set sa -f CloneSet.yaml serviceaccount1 --local --dry-run=client -o yaml"
see_also:
    - kubectl-kruise set - Set specific features on objects
