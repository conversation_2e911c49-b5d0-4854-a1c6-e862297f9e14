apiVersion: v1
kind: ReplicationController
metadata:
  annotations:
    kubectl.kubernetes.io/last-applied-configuration: |
      {"args":"-random_flag=%<EMAIL>"}
  name: test-rc
  labels:
    name: test-rc
spec:
  replicas: 1
  template:
    metadata:
      labels:
        name: test-rc
    spec:
      containers:
        - name: test-rc
          image: nginx
          args:
          - -random_flag=%<EMAIL>
          ports:
          - containerPort: 80
