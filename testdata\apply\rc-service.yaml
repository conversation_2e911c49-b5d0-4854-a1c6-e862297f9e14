apiVersion: v1
kind: List
items:
  - apiVersion: v1
    kind: Service
    metadata:
      name: test-service
      labels:
        name: test-service
    spec:
      ports:
      - port: 80
      selector:
        name: test-rc
  - apiVersion: v1
    kind: ReplicationController
    metadata:
      name: test-rc
      labels:
        name: test-rc
    spec:
      replicas: 1
      template:
        metadata:
          labels:
            name: test-rc
        spec:
          containers:
            - name: test-rc
              image: nginx
              ports:
              - containerPort: 80
