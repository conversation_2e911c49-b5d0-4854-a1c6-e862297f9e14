apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: prometheus-node-exporter
spec:
  selector:
    matchLabels:
      daemon: prom-node-exp
  template:
    metadata:
      name: prometheus-node-exporter
      labels:
        daemon: prom-node-exp
    spec:
      containers:
      - name: c
        image: prom/prometheus
        ports:
        - containerPort: 9090
          hostPort: 9090
          name: serverport
