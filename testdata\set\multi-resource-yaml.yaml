apiVersion: v1
kind: ReplicationController
metadata:
  name: first-rc
spec:
  replicas: 1
  selector:
    app: mock
  template:
    metadata:
      labels:
        app: mock
    spec:
      containers:
      - name: mock-container
        image: registry.k8s.io/pause:3.2
---
apiVersion: v1
kind: ReplicationController
metadata:
  name: second-rc
spec:
  replicas: 1
  selector:
    app: mock
  template:
    metadata:
      labels:
        app: mock
    spec:
      containers:
      - name: mock-container
        image: registry.k8s.io/pause:3.2
